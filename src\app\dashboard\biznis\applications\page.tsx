'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TabsWithBadge } from '@/components/ui/tabs-with-badge';
import { TabsContent } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import {
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  User,
  Calendar,
} from 'lucide-react';
import Link from 'next/link';
import { getBusinessCampaignApplications } from '@/lib/campaigns';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';

interface Application {
  id: string;
  campaign_id: string;
  influencer_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_rate: number;
  proposal_text: string;
  delivery_timeframe: string;
  portfolio_links: string[] | null;
  experience_relevant: string | null;
  audience_insights: string | null;
  applied_at: string;
  campaigns: {
    id: string;
    title: string;
    budget: number;
    business_id: string;
  };
  profiles: {
    id: string;
    username: string;
    full_name: string | null;
    avatar_url: string | null;
  };
}

export default function ApplicationsPage() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    if (!user) return;

    const loadApplications = async () => {
      try {
        const { data, error } = await getBusinessCampaignApplications(user.id);
        if (error) {
          console.error('Error loading applications:', error);
          return;
        }
        setApplications(data || []);
      } catch (error) {
        console.error('Error loading applications:', error);
      } finally {
        setLoading(false);
      }
    };

    loadApplications();
  }, [user]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  const filteredApplications = applications.filter(app => {
    if (activeTab === 'all') return true;
    return app.status === activeTab;
  });

  const stats = {
    total: applications.length,
    pending: applications.filter(app => app.status === 'pending').length,
    accepted: applications.filter(app => app.status === 'accepted').length,
    rejected: applications.filter(app => app.status === 'rejected').length,
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">
            Aplikacije na kampanje
          </h1>
          <p className="text-muted-foreground mt-1">
            Pregledajte i upravljajte aplikacijama influencera na vaše kampanje
          </p>
        </div>



        {/* Applications List */}
        <TabsWithBadge
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
          tabs={[
            { name: "Sve", value: "all", count: stats.total },
            { name: "Na čekanju", value: "pending", count: stats.pending },
            { name: "Prihvaćeno", value: "accepted", count: stats.accepted },
            { name: "Odbačeno", value: "rejected", count: stats.rejected }
          ]}
        >
          <TabsContent value={activeTab} className="space-y-4">
                {filteredApplications.length === 0 ? (
                  <div className="text-center py-12">
                    <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Nema aplikacija
                    </h3>
                    <p className="text-gray-600">
                      {activeTab === 'all'
                        ? 'Još uvijek nema aplikacija na vaše kampanje.'
                        : `Nema aplikacija sa statusom "${activeTab}".`}
                    </p>
                  </div>
                ) : (
                  filteredApplications.map(application => (
                      <Card
                        key={application.id}
                        className="hover:shadow-md transition-shadow"
                      >
                        <CardContent className="p-6">
                          <div className="space-y-4">
                            {/* Header with username and status */}
                            <div className="flex items-start justify-between">
                              <div className="flex items-center space-x-3">
                                <Avatar className="h-10 w-10">
                                  <AvatarImage
                                    src={application.profiles.avatar_url || ''}
                                  />
                                  <AvatarFallback>
                                    {application.profiles.username
                                      .charAt(0)
                                      .toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>
                                <h3 className="text-lg font-semibold text-gray-900">
                                  @{application.profiles.username}
                                </h3>
                              </div>

                              {/* Status in top right corner */}
                              <Badge
                                className={getStatusColor(application.status)}
                              >
                                <div className="flex items-center space-x-1">
                                  {getStatusIcon(application.status)}
                                  <span>
                                    {getStatusText(application.status)}
                                  </span>
                                </div>
                              </Badge>
                            </div>

                            {/* Campaign info */}
                            <p className="text-sm text-gray-600">
                              Kampanja:{' '}
                              <span className="font-medium">
                                {application.campaigns.title}
                              </span>
                            </p>

                            {/* Details */}
                            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                              <div className="flex items-center space-x-1">
                                <span>{application.proposed_rate} €</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Calendar className="h-4 w-4" />
                                <span>
                                  {application.delivery_timeframe}
                                </span>
                              </div>
                            </div>

                            {/* Action button at the bottom */}
                            <div className="flex items-center pt-2 border-t">
                              <Link
                                href={`/dashboard/biznis/applications/${application.id}`}
                                className="w-full"
                              >
                                <Button variant="outline" size="sm" className="w-full">
                                  <Eye className="h-4 w-4 mr-2" />
                                  Pregled
                                </Button>
                              </Link>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                )}
              </TabsContent>
            </TabsWithBadge>
      </div>
    </DashboardLayout>
  );
}
